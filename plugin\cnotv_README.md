# cnotv.com 插件使用说明

## 插件简介

cnotv.com 插件是为 PyramidStore 框架开发的视频源插件，用于从明月影院（cnotv.com）获取视频内容。

## 功能特性

- ✅ **首页内容获取** - 获取网站分类列表（电影、电视剧、综艺、动漫）
- ✅ **分类内容浏览** - 获取指定分类下的视频列表，支持分页
- ✅ **搜索功能** - 根据关键词搜索视频内容
- ✅ **视频详情获取** - 获取完整的视频信息（标题、年份、导演、主演、剧情等）
- ✅ **播放地址解析** - 获取实际的视频播放链接（M3U8格式）

## 技术特点

- **多重网络策略** - 支持 requests、cloudscraper、selenium 三种请求方式
- **智能HTML解析** - 针对不同页面结构（分类页面 vs 搜索页面）使用不同的解析逻辑
- **反爬虫处理** - 使用真实浏览器User-Agent和完整请求头
- **错误处理** - 完善的异常处理和日志记录
- **数据格式标准** - 严格遵循PyramidStore框架的数据格式要求

## 安装要求

确保已安装以下依赖：

```bash
pip install requests>=2.32.0
pip install lxml>=5.4.0
pip install cloudscraper>=1.2.71
pip install selenium>=4.10.0
pip install webdriver-manager>=3.8.6
```

## 使用方法

### 1. 基本使用

```python
from cnotv import Spider

# 初始化插件
sp = Spider()
sp.init({})

# 获取首页分类
home_result = sp.homeContent(False)
print(home_result)

# 获取分类内容
category_result = sp.categoryContent('1', '1', False, {})
print(category_result)

# 搜索视频
search_result = sp.searchContent('哪吒', False, '1')
print(search_result)

# 获取视频详情
detail_result = sp.detailContent(['115857'])
print(detail_result)

# 获取播放地址
player_result = sp.playerContent('默认播放源', '115857-1-1', [])
print(player_result)
```

### 2. TVBox配置

在TVBox配置文件中添加：

```json
{
  "sites": [
    {
      "key": "cnotv",
      "name": "明月影院",
      "type": 3,
      "api": "cnotv.py",
      "searchable": 1,
      "quickSearch": 1,
      "filterable": 0
    }
  ]
}
```

## 数据格式说明

### 首页内容格式
```json
{
  "class": [
    {
      "type_name": "电影",
      "type_id": "1"
    }
  ]
}
```

### 视频列表格式
```json
{
  "list": [
    {
      "vod_id": "115857",
      "vod_name": "哪吒之魔童闹海",
      "vod_pic": "图片URL",
      "vod_remarks": "备注信息"
    }
  ],
  "page": 1,
  "pagecount": 10,
  "limit": 20,
  "total": 200
}
```

### 视频详情格式
```json
{
  "list": [
    {
      "vod_id": "115857",
      "vod_name": "哪吒之魔童闹海",
      "vod_pic": "图片URL",
      "type_name": "动画电影",
      "vod_year": "2025",
      "vod_area": "中国大陆",
      "vod_director": "饺子",
      "vod_actor": "吕艳婷/囧森瑟夫/瀚墨",
      "vod_content": "剧情简介...",
      "vod_play_from": "默认播放源",
      "vod_play_url": "高清$115857-1-1#超清$115857-1-2"
    }
  ]
}
```

### 播放地址格式
```json
{
  "parse": 0,
  "url": "https://m3u8.hmrvideo.com/play/xxx.m3u8",
  "header": {
    "User-Agent": "...",
    "Referer": "https://cnotv.com"
  }
}
```

## 测试验证

运行测试脚本验证所有功能：

```bash
python test_cnotv.py
```

测试将验证：
1. 首页内容获取
2. 分类内容浏览
3. 搜索功能
4. 视频详情获取
5. 播放地址解析

## 注意事项

1. **网络环境** - 确保能够正常访问 cnotv.com
2. **请求频率** - 避免过于频繁的请求，建议添加适当延时
3. **User-Agent** - 插件使用真实浏览器User-Agent，如遇问题可更新
4. **错误处理** - 网络异常时会自动重试，建议监控日志输出

## 开发信息

- **开发框架**: PyramidStore
- **目标网站**: cnotv.com (明月影院)
- **网站架构**: 苹果CMS (Apple CMS)
- **支持格式**: M3U8视频流
- **开发时间**: 2025年1月

## 更新日志

### v1.0.0 (2025-01-XX)
- 初始版本发布
- 实现所有核心功能
- 完成功能测试验证
- 添加完整文档说明
