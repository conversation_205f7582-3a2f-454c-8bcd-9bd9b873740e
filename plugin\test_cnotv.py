#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
cnotv.com 插件完整功能测试脚本
"""

import json
from cnotv import Spider

def test_all_functions():
    """
    完整功能测试
    """
    print("=" * 60)
    print("cnotv.com 插件完整功能测试")
    print("=" * 60)
    
    sp = Spider()
    sp.init({})
    
    # 1. 测试首页内容
    print("\n1. 测试首页内容")
    print("-" * 30)
    try:
        home_result = sp.homeContent(False)
        categories = home_result.get('class', [])
        print(f"✅ 首页内容获取成功，共 {len(categories)} 个分类")
        for i, cat in enumerate(categories[:3]):  # 只显示前3个
            print(f"   分类 {i+1}: {cat['type_name']} (ID: {cat['type_id']})")
        if len(categories) > 3:
            print(f"   ... 还有 {len(categories) - 3} 个分类")
    except Exception as e:
        print(f"❌ 首页内容获取失败: {e}")
        return False
    
    # 2. 测试分类内容
    print("\n2. 测试分类内容")
    print("-" * 30)
    try:
        category_result = sp.categoryContent('1', '1', False, {})
        videos = category_result.get('list', [])
        print(f"✅ 分类内容获取成功，共 {len(videos)} 个视频")
        for i, video in enumerate(videos[:3]):  # 只显示前3个
            print(f"   视频 {i+1}: {video['vod_name']} (ID: {video['vod_id']})")
        if len(videos) > 3:
            print(f"   ... 还有 {len(videos) - 3} 个视频")
    except Exception as e:
        print(f"❌ 分类内容获取失败: {e}")
        return False
    
    # 3. 测试搜索功能
    print("\n3. 测试搜索功能")
    print("-" * 30)
    try:
        search_result = sp.searchContent('哪吒', False, '1')
        search_videos = search_result.get('list', [])
        print(f"✅ 搜索功能正常，找到 {len(search_videos)} 个相关视频")
        for i, video in enumerate(search_videos[:3]):  # 只显示前3个
            print(f"   搜索结果 {i+1}: {video['vod_name']} (ID: {video['vod_id']})")
        if len(search_videos) > 3:
            print(f"   ... 还有 {len(search_videos) - 3} 个搜索结果")
    except Exception as e:
        print(f"❌ 搜索功能失败: {e}")
        return False
    
    # 4. 测试视频详情
    print("\n4. 测试视频详情")
    print("-" * 30)
    if search_videos:
        try:
            test_video = search_videos[0]
            detail_result = sp.detailContent([test_video['vod_id']])
            detail = detail_result.get('list', [{}])[0]
            
            print(f"✅ 视频详情获取成功")
            print(f"   标题: {detail.get('vod_name', 'N/A')}")
            print(f"   年份: {detail.get('vod_year', 'N/A')}")
            print(f"   导演: {detail.get('vod_director', 'N/A')}")
            print(f"   主演: {detail.get('vod_actor', 'N/A')[:50]}{'...' if len(detail.get('vod_actor', '')) > 50 else ''}")
            print(f"   剧情: {detail.get('vod_content', 'N/A')[:50]}{'...' if len(detail.get('vod_content', '')) > 50 else ''}")
            print(f"   播放源: {detail.get('vod_play_from', 'N/A')}")
            
            play_url = detail.get('vod_play_url', '')
            if play_url:
                episodes = play_url.split('#')
                print(f"   播放集数: {len(episodes)} 集")
            
        except Exception as e:
            print(f"❌ 视频详情获取失败: {e}")
            return False
    else:
        print("❌ 无法测试视频详情，搜索结果为空")
        return False
    
    # 5. 测试播放地址
    print("\n5. 测试播放地址")
    print("-" * 30)
    try:
        if detail.get('vod_play_url'):
            play_from = detail.get('vod_play_from', '')
            play_url = detail.get('vod_play_url', '')
            
            # 解析第一个播放链接
            if '#' in play_url:
                first_episode = play_url.split('#')[0]
                if '$' in first_episode:
                    episode_name, play_id = first_episode.split('$', 1)
                    player_result = sp.playerContent(play_from, play_id, [])
                    
                    if player_result.get('url'):
                        print(f"✅ 播放地址获取成功")
                        print(f"   播放集数: {episode_name}")
                        print(f"   播放ID: {play_id}")
                        print(f"   播放地址: {player_result['url'][:50]}...")
                        print(f"   解析类型: {'直链播放' if player_result.get('parse') == 0 else '需要解析'}")
                    else:
                        print(f"❌ 播放地址获取失败，返回为空")
                        return False
                else:
                    print(f"❌ 播放链接格式错误")
                    return False
            else:
                print(f"❌ 播放链接格式错误")
                return False
        else:
            print(f"❌ 无播放链接可测试")
            return False
    except Exception as e:
        print(f"❌ 播放地址获取失败: {e}")
        return False
    
    print("\n" + "=" * 60)
    print("🎉 所有功能测试通过！cnotv.com 插件开发完成！")
    print("=" * 60)
    return True

if __name__ == "__main__":
    test_all_functions()
