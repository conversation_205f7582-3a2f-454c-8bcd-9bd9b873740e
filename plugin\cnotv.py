# -*- coding: utf-8 -*-
# cnotv.com 明月影院爬虫插件
# 基于PyramidStore框架开发
import sys
import re
import json
import time
from urllib.parse import quote, unquote
sys.path.append('..')
from base.spider import Spider


class Spider(Spider):
    
    def init(self, extend=""):
        """
        初始化插件配置
        TVBox配置示例:
        {
            "key": "cnotv",
            "name": "明月影院",
            "type": 3,
            "searchable": 1,
            "quickSearch": 1,
            "filterable": 1,
            "api": "./plugin/cnotv.py"
        }
        """
        self.host = "https://cnotv.com"
        self.name = "明月影院"
        
        # 分类映射
        self.categories = {
            '1': '电影',
            '2': '电视剧', 
            '3': '综艺',
            '4': '动漫'
        }
        
        # 请求头配置
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Referer': self.host,
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'same-origin',
            'Sec-Fetch-User': '?1',
            'Cache-Control': 'max-age=0'
        }
        
        self.log(f"明月影院插件初始化完成: {self.host}")

    def getName(self):
        return self.name

    def homeContent(self, filter):
        """
        获取首页内容，返回分类列表
        """
        try:
            result = {}
            
            # 构建分类列表
            classes = []
            for type_id, type_name in self.categories.items():
                classes.append({
                    'type_name': type_name,
                    'type_id': type_id
                })
            
            result['class'] = classes
            
            self.log(f"获取首页分类成功，共{len(classes)}个分类")
            return result
            
        except Exception as e:
            self.log(f"获取首页内容失败: {e}")
            return {'class': []}

    def categoryContent(self, tid, pg, filter, extend):
        """
        获取分类页面内容
        """
        try:
            page = int(pg) if pg else 1
            
            # 构建分类页面URL
            if page == 1:
                url = f"{self.host}/vodtype/{tid}/"
            else:
                url = f"{self.host}/vodtype/{tid}/page/{page}/"
            
            self.log(f"请求分类页面: {url}")
            
            # 获取页面内容
            response = self.fetch(url, headers=self.headers)
            if response.status_code != 200:
                self.log(f"请求失败，状态码: {response.status_code}")
                return self._empty_result(pg)
            
            # 解析页面内容
            videos = self._parse_video_list(response.text, is_search=False)
            
            result = {
                'list': videos,
                'page': pg,
                'pagecount': 9999,  # 暂时设置为较大值
                'limit': 20,
                'total': 999999
            }
            
            self.log(f"分类页面解析完成，获取到{len(videos)}个视频")
            return result
            
        except Exception as e:
            self.log(f"获取分类内容失败: {e}")
            return self._empty_result(pg)

    def searchContent(self, key, quick, pg="1"):
        """
        搜索功能实现
        """
        try:
            page = int(pg) if pg else 1
            
            # URL编码关键词
            encoded_key = quote(key.encode('utf-8'))
            
            # 构建搜索URL
            if page == 1:
                url = f"{self.host}/vodsearch/-------------/?wd={encoded_key}"
            else:
                url = f"{self.host}/vodsearch/-------------/?wd={encoded_key}&page={page}"
            
            self.log(f"搜索关键词: {key}, URL: {url}")
            
            # 获取搜索结果
            response = self.fetch(url, headers=self.headers)
            if response.status_code != 200:
                self.log(f"搜索请求失败，状态码: {response.status_code}")
                return self._empty_result(pg)
            
            # 解析搜索结果
            videos = self._parse_video_list(response.text, is_search=True)
            
            result = {
                'list': videos,
                'page': pg,
                'pagecount': 9999,
                'limit': 20,
                'total': 999999
            }
            
            self.log(f"搜索完成，找到{len(videos)}个结果")
            return result
            
        except Exception as e:
            self.log(f"搜索失败: {e}")
            return self._empty_result(pg)

    def detailContent(self, ids):
        """
        获取视频详情
        """
        try:
            if not ids or len(ids) == 0:
                return {'list': []}
            
            video_id = ids[0]
            url = f"{self.host}/voddetail/{video_id}/"
            
            self.log(f"获取视频详情: {url}")
            
            # 获取详情页面
            response = self.fetch(url, headers=self.headers)
            if response.status_code != 200:
                self.log(f"详情页面请求失败，状态码: {response.status_code}")
                return {'list': []}



            # 解析详情页面
            detail = self._parse_video_detail(response.text, video_id)
            
            if detail:
                self.log("视频详情解析成功")
                return {'list': [detail]}
            else:
                self.log("视频详情解析失败")
                return {'list': []}
                
        except Exception as e:
            self.log(f"获取视频详情失败: {e}")
            return {'list': []}

    def playerContent(self, flag, id, vipFlags):
        """
        获取播放地址
        """
        try:
            self.log(f"获取播放地址: flag={flag}, id={id}")
            
            # 构建播放页面URL
            url = f"{self.host}/vodplay/{id}/"
            
            self.log(f"请求播放页面: {url}")
            
            # 获取播放页面
            response = self.fetch(url, headers=self.headers)
            if response.status_code != 200:
                self.log(f"播放页面请求失败，状态码: {response.status_code}")
                return {'parse': 0, 'url': '', 'header': self.headers}
            
            # 解析播放地址
            play_url = self._parse_play_url(response.text)
            
            if play_url:
                self.log(f"获取播放地址成功: {play_url}")
                return {
                    'parse': 0,
                    'url': play_url,
                    'header': self.headers
                }
            else:
                self.log("未找到播放地址")
                return {'parse': 0, 'url': '', 'header': self.headers}
                
        except Exception as e:
            self.log(f"获取播放地址失败: {e}")
            return {'parse': 0, 'url': '', 'header': self.headers}

    def _parse_video_list(self, html_content, is_search=False):
        """
        解析视频列表页面
        """
        videos = []
        try:
            # 使用lxml解析HTML
            doc = self.html(html_content)

            # 检查页面类型
            video_containers = doc.xpath('//div[@class="module-item-pic"]')
            search_containers = doc.xpath('//div[@class="video-info"]')



            # 如果是搜索页面，优先使用搜索页面解析逻辑
            if is_search and search_containers:
                # 解析搜索页面结构（video-info）

                for container in search_containers:
                    try:
                        # 提取标题链接
                        title_elem = container.xpath('.//h3/a[contains(@href, "/voddetail/")]')
                        if not title_elem:
                            continue

                        href = title_elem[0].get('href', '')
                        title = title_elem[0].get('title', '').strip()

                        # 提取视频ID
                        video_id = self._extract_video_id(href)
                        if not video_id or not title:
                            continue

                        # 提取备注信息（集数状态）
                        remarks = ""
                        serial_elem = container.xpath('.//a[@class="video-serial"]')
                        if serial_elem and serial_elem[0].text:
                            remarks = serial_elem[0].text.strip()

                        # 搜索页面通常没有封面图片，使用默认值
                        pic = ""

                        videos.append({
                            'vod_id': video_id,
                            'vod_name': title,
                            'vod_pic': pic,
                            'vod_remarks': remarks
                        })

                    except Exception as e:
                        self.log(f"解析搜索结果项目失败: {e}")
                        continue

            elif video_containers:
                # 分类页面结构
                for container in video_containers:
                    try:
                        # 提取视频链接和标题
                        link_elem = container.xpath('.//a[contains(@href, "/voddetail/")]')
                        if not link_elem:
                            continue

                        href = link_elem[0].get('href', '')
                        title = link_elem[0].get('title', '').strip()

                        # 提取视频ID
                        video_id = self._extract_video_id(href)
                        if not video_id or not title:
                            continue

                        # 提取封面图片 - 从style属性中提取background-image
                        pic = ""
                        style = link_elem[0].get('style', '')
                        if 'background:' in style and 'url(' in style:
                            # 提取background-image URL
                            import re
                            pic_match = re.search(r'url\(([^)]+)\)', style)
                            if pic_match:
                                pic = pic_match.group(1).strip()

                        # 如果没有找到背景图片，尝试查找img标签
                        if not pic:
                            img_elem = container.xpath('.//img')
                            if img_elem:
                                pic = img_elem[0].get('data-src', '') or img_elem[0].get('src', '')

                        # 查找备注信息（更新状态等）
                        remarks = ""
                        # 在父容器中查找备注信息
                        parent = container.getparent()
                        if parent is not None:
                            remarks_elem = parent.xpath('.//div[@class="module-item-note"]')
                            if remarks_elem and remarks_elem[0].text:
                                remarks = remarks_elem[0].text.strip()

                        videos.append({
                            'vod_id': video_id,
                            'vod_name': title,
                            'vod_pic': pic,
                            'vod_remarks': remarks
                        })

                    except Exception as e:
                        self.log(f"解析单个视频项目失败: {e}")
                        continue


        except Exception as e:
            self.log(f"解析视频列表失败: {e}")

        return videos

    def _parse_video_detail(self, html_content, video_id):
        """
        解析视频详情页面
        """
        try:
            doc = self.html(html_content)
            
            # 提取基本信息
            title_elem = doc.xpath('//h1[@class="page-title"]')
            title = title_elem[0].text.strip() if title_elem and title_elem[0].text else ""
            
            # 提取封面
            pic_elem = doc.xpath('//div[@class="video-info-pic"]//img')
            pic = pic_elem[0].get('data-src', '') if pic_elem else ''
            if not pic:
                pic = pic_elem[0].get('src', '') if pic_elem else ''
            
            # 提取详细信息
            info_items = doc.xpath('//div[@class="video-info-items"]')

            type_name = ""
            year = ""
            area = ""
            director = ""
            actor = ""
            content = ""

            for item in info_items:
                title_elem = item.xpath('.//span[@class="video-info-itemtitle"]')
                if not title_elem:
                    continue

                item_title = title_elem[0].text.strip() if title_elem[0].text else ""

                if "导演：" in item_title:
                    # 提取导演信息
                    director_links = item.xpath('.//a')
                    directors = [link.text.strip() for link in director_links if link.text and link.text.strip()]
                    director = "/".join(directors)

                elif "主演：" in item_title:
                    # 提取主演信息
                    actor_links = item.xpath('.//a')
                    actors = [link.text.strip() for link in actor_links if link.text and link.text.strip()]
                    actor = "/".join(actors)

                elif "上映：" in item_title:
                    # 提取年份
                    year_elem = item.xpath('.//div[@class="video-info-item"]')
                    if year_elem and year_elem[0].text:
                        year = year_elem[0].text.strip()

                elif "剧情：" in item_title:
                    # 提取剧情简介
                    content_elem = item.xpath('.//div[contains(@class,"video-info-content")]//span')
                    if content_elem and content_elem[0].text:
                        content = content_elem[0].text.strip()
            
            # 提取播放列表
            play_from, play_url = self._parse_play_list(doc)
            
            detail = {
                'vod_id': video_id,
                'vod_name': title,
                'vod_pic': pic,
                'type_name': type_name,
                'vod_year': year,
                'vod_area': area,
                'vod_director': director,
                'vod_actor': actor,
                'vod_content': content,
                'vod_play_from': play_from,
                'vod_play_url': play_url
            }
            
            return detail
            
        except Exception as e:
            self.log(f"解析视频详情失败: {e}")
            return None

    def _parse_play_list(self, doc):
        """
        解析播放列表
        """
        try:
            play_from_list = []
            play_url_list = []

            # 查找播放源标签
            tab_items = doc.xpath('//div[@class="module-tab-item"]')

            if tab_items:
                for i, tab in enumerate(tab_items):
                    # 获取线路名称
                    tab_name = tab.get('data-dropdown-value', '').strip()
                    if not tab_name:
                        span_elem = tab.xpath('.//span')
                        tab_name = span_elem[0].text.strip() if span_elem and span_elem[0].text else f"线路{i+1}"

                    play_from_list.append(tab_name)

                    # 获取播放链接 - 查找所有vodplay链接
                    play_links = doc.xpath('//a[contains(@href, "/vodplay/")]')

                    episodes = []
                    for play_item in play_links:
                        episode_title = ""
                        # 尝试从span中获取标题
                        span_elem = play_item.xpath('.//span')
                        if span_elem and span_elem[0].text:
                            episode_title = span_elem[0].text.strip()
                        else:
                            episode_title = play_item.get('title', '').replace('播放', '').replace('哪吒之魔童闹海', '').strip()

                        episode_url = play_item.get('href', '')

                        if episode_title and episode_url:
                            # 提取播放ID
                            play_id = self._extract_play_id(episode_url)
                            if play_id:
                                episodes.append(f"{episode_title}${play_id}")

                    play_url_list.append('#'.join(episodes))
            else:
                # 如果没有找到标签，直接查找播放链接
                play_links = doc.xpath('//a[contains(@href, "/vodplay/")]')
                if play_links:
                    play_from_list.append("默认播放源")

                    episodes = []
                    for play_item in play_links:
                        episode_title = ""
                        # 尝试从span中获取标题
                        span_elem = play_item.xpath('.//span')
                        if span_elem and span_elem[0].text:
                            episode_title = span_elem[0].text.strip()
                        else:
                            episode_title = play_item.get('title', '').replace('播放', '').replace('哪吒之魔童闹海', '').strip()
                            if not episode_title:
                                episode_title = "播放"

                        episode_url = play_item.get('href', '')

                        if episode_url:
                            # 提取播放ID
                            play_id = self._extract_play_id(episode_url)
                            if play_id:
                                episodes.append(f"{episode_title}${play_id}")

                    play_url_list.append('#'.join(episodes))

            play_from = '$$$'.join(play_from_list)
            play_url = '$$$'.join(play_url_list)

            return play_from, play_url
            
        except Exception as e:
            self.log(f"解析播放列表失败: {e}")
            return "", ""

    def _parse_play_url(self, html_content):
        """
        解析播放页面获取真实播放地址
        """
        try:
            # 从页面中提取m3u8地址或其他播放链接
            # 这里需要根据实际网站的播放器实现来调整
            
            # 方法1: 正则匹配JavaScript中的播放地址
            m3u8_pattern = r'["\']([^"\']*\.m3u8[^"\']*)["\']'
            matches = re.findall(m3u8_pattern, html_content)
            
            if matches:
                return matches[0]
            
            # 方法2: 查找其他视频格式
            video_patterns = [
                r'["\']([^"\']*\.mp4[^"\']*)["\']',
                r'["\']([^"\']*\.flv[^"\']*)["\']',
                r'url["\']?\s*[:=]\s*["\']([^"\']+)["\']'
            ]
            
            for pattern in video_patterns:
                matches = re.findall(pattern, html_content)
                if matches:
                    return matches[0]
            
            return ""
            
        except Exception as e:
            self.log(f"解析播放地址失败: {e}")
            return ""

    def _extract_video_id(self, href):
        """
        从链接中提取视频ID
        """
        try:
            # 匹配 /voddetail/123456/ 格式
            match = re.search(r'/voddetail/(\d+)/', href)
            if match:
                return match.group(1)
            return ""
        except:
            return ""

    def _extract_play_id(self, href):
        """
        从播放链接中提取播放ID
        """
        try:
            # 匹配 /vodplay/123456-1-1/ 格式
            match = re.search(r'/vodplay/([^/]+)/', href)
            if match:
                return match.group(1)
            return ""
        except:
            return ""

    def _empty_result(self, pg):
        """
        返回空结果
        """
        return {
            'list': [],
            'page': pg,
            'pagecount': 0,
            'limit': 20,
            'total': 0
        }
